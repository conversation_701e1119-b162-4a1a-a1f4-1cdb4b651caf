package com.polarion.synchronizer.proxy.feishu;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import org.apache.commons.io.IOUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import com.lark.project.core.request.RequestOptions;
import com.lark.project.service.attachment.AttachmentService;
import com.lark.project.service.attachment.builder.DownloadAttachmentReq;
import com.lark.project.service.attachment.builder.DownloadAttachmentResp;
import com.lark.project.service.attachment.builder.UploadAttachmentReq;
import com.lark.project.service.attachment.builder.UploadAttachmentResp;
import com.polarion.core.util.logging.Logger;
import com.polarion.synchronizer.model.Attachment;
import com.polarion.synchronizer.model.IAttachmentLoader;
import com.polarion.synchronizer.model.UpdateResult;

/**
 * 飞书附件处理器
 * 参考 JIRA 的 AbstractTransferHelper 实现模式
 */
public class FeishuAttachmentHandler {
    
    private static final Logger logger = Logger.getLogger(FeishuAttachmentHandler.class);
    
    @NotNull
    private final AttachmentService attachmentService;
    
    @NotNull
    private final FeishuProxyConfiguration configuration;
    
    @NotNull
    private final Map<String, String> attachmentIdNameMap;

    public FeishuAttachmentHandler(@NotNull AttachmentService attachmentService,
                                  @NotNull FeishuProxyConfiguration configuration) {
        this.attachmentService = attachmentService;
        this.configuration = configuration;
        this.attachmentIdNameMap = new HashMap<>();
    }

    /**
     * 处理附件上传（参考 JIRA 的 processAttachments 方法）
     */
    @NotNull
    public UpdateResult processAttachments(@NotNull String workItemId,
                                          @NotNull String workItemTypeKey,
                                          @NotNull Collection<Attachment> attachments) {
        UpdateResult result = UpdateResult.success();
        
        if (attachments.isEmpty()) {
            return result;
        }
        
        logger.debug("开始处理附件上传，数量: " + attachments.size());
        
        for (Attachment attachment : attachments) {
            try {
                UpdateResult uploadResult = uploadSingleAttachment(workItemId, workItemTypeKey, attachment);
                if (uploadResult.hasError()) {
                    result.addError("attachments", "附件上传失败: " + attachment.getFileName() +
                                   " - " + uploadResult.getError());
                }
            } catch (Exception e) {
                logger.error("上传附件失败: " + attachment.getFileName(), e);
                result.addError("attachments", "附件上传异常: " + attachment.getFileName() +
                               " - " + e.getMessage());
            }
        }
        
        return result;
    }

    /**
     * 上传单个附件
     */
    @NotNull
    private UpdateResult uploadSingleAttachment(@NotNull String workItemId,
                                               @NotNull String workItemTypeKey,
                                               @NotNull Attachment attachment) {
        try {
            logger.debug("上传附件: " + attachment.getFileName());
            
            // 创建上传请求
            UploadAttachmentReq.Builder reqBuilder = UploadAttachmentReq.newBuilder()
                .projectKey(configuration.getProjectKey())
                .workItemTypeKey(workItemTypeKey)
                .workItemID(Long.parseLong(workItemId));

            // 设置文件内容
            try (InputStream contentStream = attachment.getContent()) {
                if (contentStream != null) {
                    // 将 InputStream 转换为字节数组
                    byte[] content = IOUtils.toByteArray(contentStream);
                    if (content.length > 0) {
                        // 创建临时文件
                        File tempFile = createTempFile(attachment.getFileName(), content);
                        reqBuilder.file(tempFile);
                    } else {
                        logger.warn("附件内容为空: " + attachment.getFileName());
                        return new UpdateResult("附件内容为空");
                    }
                } else {
                    logger.warn("附件内容流为空: " + attachment.getFileName());
                    return new UpdateResult("附件内容流为空");
                }
            } catch (Exception e) {
                logger.error("读取附件内容失败: " + attachment.getFileName(), e);
                return new UpdateResult("读取附件内容失败: " + e.getMessage());
            }
            
            UploadAttachmentReq request = reqBuilder.build();
            
            // 执行上传
            RequestOptions options = new RequestOptions();
            UploadAttachmentResp response = attachmentService.uploadAttachment(request, options);

            if (response != null && response.success()) {
                logger.debug("附件上传成功: " + attachment.getFileName());

                // 更新附件映射（假设响应中包含文件ID）
                // 这里需要根据实际的响应结构来获取文件ID
                attachmentIdNameMap.put(workItemId + "_" + attachment.getFileName(), attachment.getFileName());

                return UpdateResult.success();
            } else {
                String errorMsg = response != null ? "上传请求失败" : "未知错误";
                logger.error("附件上传失败: " + attachment.getFileName() + " - " + errorMsg);
                return new UpdateResult("上传失败: " + errorMsg);
            }
            
        } catch (Exception e) {
            logger.error("上传附件时发生异常: " + attachment.getFileName(), e);
            return new UpdateResult("上传异常: " + e.getMessage());
        }
    }

    /**
     * 下载附件内容（参考 JIRA 的 loadAttachmentContent 实现）
     */
    @Nullable
    public byte[] downloadAttachmentContent(@NotNull String fileId,
                                           @NotNull String workItemId,
                                           @NotNull String workItemTypeKey) {
        try {
            logger.debug("下载附件内容: " + fileId);

            // 创建下载请求
            DownloadAttachmentReq.Builder reqBuilder = DownloadAttachmentReq.newBuilder()
                .projectKey(configuration.getProjectKey())
                .workItemTypeKey(workItemTypeKey)
                .workItemID(Long.parseLong(workItemId));

            DownloadAttachmentReq request = reqBuilder.build();

            // 执行下载
            RequestOptions options = new RequestOptions();
            DownloadAttachmentResp response = attachmentService.downloadAttachment(request, options);

            if (response != null && response.getData() != null) {
                // 从响应中获取文件内容
                byte[] content = response.getData().toByteArray();
                logger.debug("附件下载成功: " + fileId + ", 大小: " + content.length + " bytes");
                return content;
            } else {
                logger.warn("附件下载失败，响应为空: " + fileId);
                return null;
            }

        } catch (Exception e) {
            logger.error("下载附件失败: " + fileId, e);
            return null;
        }
    }

    /**
     * 下载附件内容（简化版本，用于URL下载）
     */
    @Nullable
    public byte[] downloadAttachmentContent(@NotNull String fileId) {
        // 这个方法需要工作项信息，暂时返回 null
        logger.warn("需要工作项信息才能下载附件: " + fileId);
        return null;
    }

    /**
     * 下载附件并创建 Attachment 对象（参考 JIRA 的实现）
     */
    @Nullable
    public Attachment downloadAttachment(@NotNull String fileId,
                                        @NotNull String fileName,
                                        @NotNull String workItemId,
                                        @NotNull String workItemTypeKey) {
        try {
            logger.debug("下载附件: " + fileName + " (ID: " + fileId + ")");

            // 创建 Attachment 对象，使用延迟加载内容（参考 JIRA 的实现）
            IAttachmentLoader loader = new IAttachmentLoader() {
                @Override
                public byte[] load() throws Exception {
                    byte[] content = downloadAttachmentContent(fileId, workItemId, workItemTypeKey);
                    return content != null ? content : new byte[0];
                }
            };

            Attachment attachment = new Attachment(fileName, loader);

            return attachment;

        } catch (Exception e) {
            logger.error("创建附件对象失败: " + fileName, e);
            return null;
        }
    }

    /**
     * 获取附件ID到名称的映射
     */
    @NotNull
    public Map<String, String> getAttachmentIdNameMap() {
        return new HashMap<>(attachmentIdNameMap);
    }

    /**
     * 清空附件映射
     */
    public void clearAttachmentMapping() {
        attachmentIdNameMap.clear();
        logger.debug("清空附件映射");
    }

    /**
     * 添加附件映射
     */
    public void addAttachmentMapping(@NotNull String fileId, @NotNull String fileName) {
        attachmentIdNameMap.put(fileId, fileName);
        logger.debug("添加附件映射: " + fileId + " -> " + fileName);
    }

    /**
     * 编码文件名（参考 JIRA 的实现）
     */
    @NotNull
    private String encodeFileName(@NotNull String fileName) {
        try {
            // 简单的文件名编码，避免特殊字符问题
            return fileName.replaceAll("[^a-zA-Z0-9._-]", "_");
        } catch (Exception e) {
            logger.warn("文件名编码失败: " + fileName, e);
            return fileName;
        }
    }

    /**
     * 创建临时文件
     */
    @NotNull
    private File createTempFile(@NotNull String fileName, @NotNull byte[] content) throws Exception {
        // 创建临时文件
        String prefix = "feishu_attachment_";
        String suffix = getFileExtension(fileName);
        File tempFile = File.createTempFile(prefix, suffix);

        // 写入内容
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            fos.write(content);
        }

        // 设置删除标记
        tempFile.deleteOnExit();

        logger.debug("创建临时文件: " + tempFile.getAbsolutePath());
        return tempFile;
    }

    /**
     * 获取文件扩展名
     */
    @NotNull
    private String getFileExtension(@NotNull String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex);
        }
        return ".tmp";
    }

    /**
     * 验证附件
     */
    private boolean validateAttachment(@NotNull Attachment attachment) {
        if (attachment.getFileName() == null || attachment.getFileName().trim().isEmpty()) {
            logger.warn("附件文件名为空");
            return false;
        }

        try (InputStream contentStream = attachment.getContent()) {
            if (contentStream == null) {
                logger.warn("附件内容流为空: " + attachment.getFileName());
                return false;
            }

            // 检查是否有内容（读取第一个字节）
            int firstByte = contentStream.read();
            if (firstByte == -1) {
                logger.warn("附件内容为空: " + attachment.getFileName());
                return false;
            }

            // 可以添加更多验证逻辑，如文件大小限制、文件类型检查等
            return true;

        } catch (Exception e) {
            logger.warn("验证附件时发生异常: " + attachment.getFileName(), e);
            return false;
        }
    }
}
